// uiComponents.tsx
// Complete UI Components Library for Jobbify
// Includes: Badge, Button, Card, ProfileBadge, ActionCard, and other reusable UI elements

import React from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  ActivityIndicator, 
  Image,
  ViewStyle, 
  TextStyle, 
  StyleProp 
} from 'react-native';
import { MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';

// ============================================================================
// BADGE COMPONENT
// ============================================================================

interface BadgeProps {
  label: string;
  showClock?: boolean;
  style?: any;
  textStyle?: any;
}

export const Badge: React.FC<BadgeProps> = ({ 
  label, 
  showClock = false, 
  style,
  textStyle 
}) => {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  const getBadgeColor = (apiSource: string) => {
    switch (apiSource.toLowerCase()) {
      case 'jooble':
        return '#4CAF50';
      case 'adzuna':
        return '#2196F3';
      case 'muse':
        return '#9C27B0';
      case 'arbeitnow':
        return '#FF9800';
      case 'remoteok':
        return '#F44336';
      default:
        return themeColors.textSecondary;
    }
  };

  const badgeColor = getBadgeColor(label);

  return (
    <View 
      style={[
        badgeStyles.badge,
        { 
          backgroundColor: badgeColor + '20',
          borderColor: badgeColor + '40',
        },
        style
      ]}
    >
      {showClock && (
        <MaterialIcons 
          name="schedule" 
          size={12} 
          color={badgeColor}
          style={badgeStyles.clockIcon}
        />
      )}
      <Text 
        style={[
          badgeStyles.badgeText,
          { color: badgeColor },
          textStyle
        ]}
      >
        {label.toUpperCase()}
      </Text>
    </View>
  );
};

const badgeStyles = StyleSheet.create({
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    opacity: 0.6,
  },
  clockIcon: {
    marginRight: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
});

// ============================================================================
// BUTTON COMPONENT
// ============================================================================

interface ButtonProps {
  title: string;
  onPress: () => void;
  type?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  type = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  icon,
}) => {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  // Define button styles based on type
  const getButtonStyle = (): ViewStyle => {
    switch (type) {
      case 'primary':
        return {
          backgroundColor: themeColors.tint,
          borderColor: themeColors.tint,
          borderWidth: 1,
        };
      case 'secondary':
        return {
          backgroundColor: 'transparent',
          borderColor: themeColors.tint,
          borderWidth: 1,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: themeColors.border,
          borderWidth: 1,
        };
      case 'text':
        return {
          backgroundColor: 'transparent',
          borderWidth: 0,
        };
      default:
        return {
          backgroundColor: themeColors.tint,
          borderColor: themeColors.tint,
          borderWidth: 1,
        };
    }
  };
  
  // Define text styles based on type
  const getTextStyle = (): TextStyle => {
    switch (type) {
      case 'primary':
        return { color: '#FFFFFF' };
      case 'secondary':
      case 'outline':
      case 'text':
        return { color: themeColors.tint };
      default:
        return { color: '#FFFFFF' };
    }
  };
  
  // Define button size
  const getButtonSize = (): ViewStyle => {
    switch (size) {
      case 'small':
        return { paddingVertical: 8, paddingHorizontal: 16, borderRadius: 4 };
      case 'medium':
        return { paddingVertical: 12, paddingHorizontal: 24, borderRadius: 6 };
      case 'large':
        return { paddingVertical: 16, paddingHorizontal: 32, borderRadius: 8 };
      default:
        return { paddingVertical: 12, paddingHorizontal: 24, borderRadius: 6 };
    }
  };
  
  // Define text size
  const getTextSize = (): TextStyle => {
    switch (size) {
      case 'small':
        return { fontSize: 14 };
      case 'medium':
        return { fontSize: 16 };
      case 'large':
        return { fontSize: 18 };
      default:
        return { fontSize: 16 };
    }
  };

  // Create style arrays with proper typing
  const buttonStyles: StyleProp<ViewStyle>[] = [
    buttonBaseStyles.button,
    getButtonStyle(),
    getButtonSize(),
  ];
  
  if (disabled) {
    buttonStyles.push(buttonBaseStyles.disabled);
  }
  
  if (style) {
    buttonStyles.push(style);
  }
  
  const textStyles: StyleProp<TextStyle>[] = [
    buttonBaseStyles.text,
    getTextStyle(),
    getTextSize(),
  ];
  
  if (icon) {
    textStyles.push({ marginLeft: 8 } as TextStyle);
  }
  
  if (textStyle) {
    textStyles.push(textStyle);
  }
  
  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator color={getTextStyle().color} size="small" />
      ) : (
        <>
          {icon && <>{icon}</>}
          <Text style={textStyles}>{title}</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const buttonBaseStyles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});

// ============================================================================
// CARD COMPONENT
// ============================================================================

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
  elevation?: number;
  borderRadius?: number;
  padding?: number;
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  onPress,
  elevation = 2,
  borderRadius = 12,
  padding = 16,
}) => {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const cardStyle = {
    backgroundColor: themeColors.card,
    borderColor: themeColors.border,
    borderRadius,
    padding,
    ...getShadow(elevation, theme),
  };
  
  if (onPress) {
    return (
      <TouchableOpacity
        style={[cardStyles.card, cardStyle, style]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        {children}
      </TouchableOpacity>
    );
  }
  
  return (
    <View style={[cardStyles.card, cardStyle, style]}>
      {children}
    </View>
  );
};

// Helper function to get shadow styles based on elevation and theme
function getShadow(elevation: number, theme: 'light' | 'dark') {
  if (theme === 'dark') {
    // Subtle shadow for dark theme
    return {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: elevation },
      shadowOpacity: 0.3,
      shadowRadius: elevation * 1.5,
      elevation: elevation,
    };
  }
  
  // More visible shadow for light theme
  return {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: elevation },
    shadowOpacity: 0.1,
    shadowRadius: elevation,
    elevation: elevation,
  };
}

const cardStyles = StyleSheet.create({
  card: {
    borderWidth: 0.5,
    marginVertical: 8,
    overflow: 'hidden',
  },
});

// ============================================================================
// PROFILE BADGE COMPONENT
// ============================================================================

interface ProfileBadgeProps {
  uri: string;
  size?: number;
  completionPercent?: number;
  showCompletionIndicator?: boolean;
  themeColors: any;
  onPress?: () => void;
}

export const ProfileBadge: React.FC<ProfileBadgeProps> = ({
  uri,
  size = 44,
  completionPercent = 0,
  showCompletionIndicator = true,
  themeColors,
  onPress,
}) => {
  // Status color based on completion
  let statusColor = '#F44336'; // Red - needs attention
  if (completionPercent >= 80) {
    statusColor = '#4CAF50'; // Green - good
  } else if (completionPercent >= 40) {
    statusColor = '#FF9800'; // Orange - in progress
  }

  return (
    <TouchableOpacity
      style={[profileBadgeStyles.container, { width: size, height: size }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <Image
        source={{ uri }}
        style={[
          profileBadgeStyles.avatar,
          {
            width: size,
            height: size,
            borderColor: themeColors.card,
          }
        ]}
      />

      {showCompletionIndicator && (
        <View
          style={[
            profileBadgeStyles.statusBadge,
            {
              backgroundColor: statusColor,
              borderColor: themeColors.background,
            }
          ]}
        />
      )}
    </TouchableOpacity>
  );
};

const profileBadgeStyles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  avatar: {
    borderRadius: 999,
    borderWidth: 2,
  },
  statusBadge: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    bottom: 0,
    right: 0,
  },
});

// ============================================================================
// ACTION CARD COMPONENT
// ============================================================================

interface ActionCardProps {
  title: string;
  description: string;
  icon: any;
  onPress: () => void;
  themeColors: any;
  primary?: boolean;
}

export const ActionCard: React.FC<ActionCardProps> = ({
  title,
  description,
  icon,
  onPress,
  themeColors,
  primary = false,
}) => {
  return (
    <TouchableOpacity
      style={[
        actionCardStyles.container,
        {
          backgroundColor: primary ? themeColors.tint + '15' : themeColors.card,
          borderColor: primary ? themeColors.tint : 'transparent',
        },
      ]}
      activeOpacity={0.7}
      onPress={onPress}
    >
      <View
        style={[
          actionCardStyles.iconContainer,
          {
            backgroundColor: primary ? themeColors.tint + '25' : themeColors.background + '80',
          },
        ]}
      >
        <FontAwesome
          name={icon}
          size={20}
          color={primary ? themeColors.tint : themeColors.text}
        />
      </View>
      <View style={actionCardStyles.contentContainer}>
        <Text
          style={[
            actionCardStyles.title,
            {
              color: primary ? themeColors.tint : themeColors.text,
              fontWeight: primary ? '700' : '600',
            },
          ]}
        >
          {title}
        </Text>
        <Text
          style={[
            actionCardStyles.description,
            { color: themeColors.textSecondary },
          ]}
          numberOfLines={2}
        >
          {description}
        </Text>
      </View>
      <FontAwesome
        name="chevron-right"
        size={14}
        color={themeColors.textSecondary}
        style={actionCardStyles.chevron}
      />
    </TouchableOpacity>
  );
};

const actionCardStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 6,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  chevron: {
    marginLeft: 8,
  },
});

// ============================================================================
// CIRCULAR PROGRESS COMPONENT
// ============================================================================

interface CircularProgressProps {
  size?: number;
  progress: number; // 0 to 1
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  children?: React.ReactNode;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  size = 100,
  progress,
  strokeWidth = 8,
  color = '#4CAF50',
  backgroundColor = '#E0E0E0',
  children,
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress * circumference);

  return (
    <View style={[circularProgressStyles.container, { width: size, height: size }]}>
      <View style={circularProgressStyles.svgContainer}>
        {/* Background circle */}
        <View
          style={[
            circularProgressStyles.circle,
            {
              width: size - strokeWidth,
              height: size - strokeWidth,
              borderRadius: (size - strokeWidth) / 2,
              borderWidth: strokeWidth,
              borderColor: backgroundColor,
            }
          ]}
        />
        {/* Progress circle */}
        <View
          style={[
            circularProgressStyles.progressCircle,
            {
              width: size - strokeWidth,
              height: size - strokeWidth,
              borderRadius: (size - strokeWidth) / 2,
              borderWidth: strokeWidth,
              borderColor: color,
              transform: [{ rotate: '-90deg' }],
            }
          ]}
        />
      </View>
      {children && (
        <View style={circularProgressStyles.content}>
          {children}
        </View>
      )}
    </View>
  );
};

const circularProgressStyles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  svgContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  circle: {
    position: 'absolute',
  },
  progressCircle: {
    position: 'absolute',
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
