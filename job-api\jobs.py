# jobs.py
from fastapi import APIRouter, HTTPException, Body
from job_service import fetch_and_store_jobs, TEST_JOBS
from supabase_client import supabase
from fastapi.responses import JSONResponse
from typing import Dict, Any, List
import uuid
import asyncio
from datetime import datetime
from job_filter import filter_jobs_for_display, validate_job_for_panel_display
from pydantic import BaseModel, validator
from typing import Optional

router = APIRouter(prefix="/jobs", tags=["jobs"])

# Import necessary classes and functions
class JobSearchRequest(BaseModel):
    keywords: str
    location: str
    job_type: Optional[str] = None
    experience_level: Optional[str] = None
    min_salary: Optional[int] = None
    max_salary: Optional[int] = None

@router.post("/refresh")
def refresh_jobs():
    """DEPRECATED: Static job storage removed. Use /jobs/personalized-search instead."""
    return {"message": "Static job storage deprecated. Use personalized search endpoints.", "deprecated": True}

async def search_jobs_realtime(request: JobSearchRequest) -> Dict[str, Any]:
    """Search for jobs in real-time based on user preferences"""
    import httpx
    from job_apis import jooble, map_jooble_job, muse, map_muse_job, ashby, map_ashby_job
    from job_service import adzuna, arbeitnow, map_adzuna_job, map_arbeitnow_job

    try:
        print(f"🔍 Real-time job search: {request.keywords} in {request.location}")

        # Fetch jobs from multiple APIs concurrently
        async with httpx.AsyncClient() as client:
            tasks = []

            # Jooble API
            tasks.append(jooble(client, request.keywords, request.location))

            # The Muse API (if relevant category)
            if any(keyword in request.keywords.lower() for keyword in ['technology', 'computer', 'software', 'developer', 'engineer']):
                tasks.append(muse(client, "Computer and IT", request.location))
            else:
                tasks.append(muse(client, "Business", request.location))

            # Adzuna API
            tasks.append(adzuna(client))

            # Arbeitnow API
            tasks.append(arbeitnow(client))

            # Execute all API calls concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            all_jobs = []

            # Process Jooble results
            if len(results) > 0 and not isinstance(results[0], Exception):
                jooble_jobs = results[0]
                for job in jooble_jobs:
                    mapped_job = map_jooble_job(job)
                    all_jobs.append(mapped_job)

            # Process Muse results
            if len(results) > 1 and not isinstance(results[1], Exception):
                muse_jobs = results[1]
                for job in muse_jobs:
                    mapped_job = map_muse_job(job)
                    all_jobs.append(mapped_job)

            # Process Adzuna results
            if len(results) > 2 and not isinstance(results[2], Exception):
                adzuna_jobs = results[2]
                for job in adzuna_jobs:
                    mapped_job = map_adzuna_job(job)
                    all_jobs.append(mapped_job)

            # Process Arbeitnow results
            if len(results) > 3 and not isinstance(results[3], Exception):
                arbeitnow_jobs = results[3]
                for job in arbeitnow_jobs:
                    mapped_job = map_arbeitnow_job(job)
                    all_jobs.append(mapped_job)

        # Filter jobs based on search criteria
        filtered_jobs = filter_jobs_by_criteria(all_jobs, request)

        # Remove duplicates
        unique_jobs = remove_duplicate_jobs(filtered_jobs)

        # Sort by relevance
        sorted_jobs = sort_jobs_by_relevance(unique_jobs, request)

        print(f"✅ Found {len(sorted_jobs)} relevant jobs")

        return {
            "jobs": sorted_jobs[:50],  # Limit to 50 jobs
            "total_found": len(sorted_jobs),
            "search_params": request.dict()
        }

    except Exception as e:
        print(f"❌ Error in real-time job search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Job search failed: {str(e)}")

def filter_jobs_by_criteria(jobs: List[Dict], request: JobSearchRequest) -> List[Dict]:
    """Filter jobs based on search criteria"""
    filtered = []

    for job in jobs:
        try:
            # Location filter
            if request.location.lower() != 'remote':
                job_location = job.get('location', '')
                # Safely convert to string and lowercase
                if isinstance(job_location, dict):
                    job_location = str(job_location.get('display_name', ''))
                job_location = str(job_location).lower()

                if request.location.lower() not in job_location and 'remote' not in job_location:
                    continue

            # Salary filter
            if request.min_salary or request.max_salary:
                job_salary = job.get('salary', '')
                if job_salary:
                    # Extract salary numbers (basic implementation)
                    import re
                    salary_numbers = re.findall(r'\d+', str(job_salary))
                    if salary_numbers:
                        job_salary_num = int(salary_numbers[0])
                        if request.min_salary and job_salary_num < request.min_salary:
                            continue
                        if request.max_salary and job_salary_num > request.max_salary:
                            continue

            # Job type filter
            if request.job_type:
                job_title = job.get('title', '')
                job_description = job.get('description', '')

                # Safely convert to string and lowercase
                job_title = str(job_title).lower()
                job_description = str(job_description).lower()

                if request.job_type.lower() not in job_title and request.job_type.lower() not in job_description:
                    continue

            filtered.append(job)
        except Exception as e:
            print(f"⚠️ Error filtering job {job.get('title', 'Unknown')}: {e}")
            # Skip this job if there's an error processing it
            continue

    return filtered

def remove_duplicate_jobs(jobs: List[Dict]) -> List[Dict]:
    """Remove duplicate jobs based on title and company"""
    seen = set()
    unique_jobs = []

    for job in jobs:
        try:
            title = str(job.get('title', '')).lower().strip()
            company = str(job.get('company', '')).lower().strip()
            key = f"{title}_{company}"

            if key not in seen:
                seen.add(key)
                unique_jobs.append(job)
        except Exception as e:
            print(f"⚠️ Error processing job for deduplication: {e}")
            # Add the job anyway to avoid losing data
            unique_jobs.append(job)

    return unique_jobs

def sort_jobs_by_relevance(jobs: List[Dict], request: JobSearchRequest) -> List[Dict]:
    """Sort jobs by relevance to search criteria"""
    def calculate_relevance_score(job):
        try:
            score = 0
            title = str(job.get('title', '')).lower()
            description = str(job.get('description', '')).lower()
            location = str(job.get('location', '')).lower()

            # Keyword relevance
            keywords = request.keywords.lower().split()
            for keyword in keywords:
                if keyword in title:
                    score += 10
                if keyword in description:
                    score += 5

            # Location relevance
            if request.location.lower() in location:
                score += 8
            elif 'remote' in location and request.location.lower() == 'remote':
                score += 10

            # Recent posting bonus (if available)
            if 'posted_date' in job:
                # Add bonus for recent postings
                score += 2

            return score
        except Exception as e:
            print(f"⚠️ Error calculating relevance for job {job.get('title', 'Unknown')}: {e}")
            return 0

    # Sort by relevance score (descending)
    return sorted(jobs, key=calculate_relevance_score, reverse=True)

@router.get("/for-user/{user_id}")
async def get_jobs_for_user(user_id: str, limit: int = 20):
    """Get personalized jobs for a user based on their preferences"""

    try:
        print(f"🔍 Looking for preferences for user: {user_id}")

        # Debug: Check supabase client
        import os
        print(f"🔧 SUPABASE_URL: {os.getenv('SUPABASE_URL')}")
        print(f"🔧 SUPABASE_KEY exists: {bool(os.getenv('SUPABASE_KEY'))}")

        # Test: First try to get all records from the table
        test_resp = supabase.table("user_job_preferences").select("user_id").limit(5).execute()
        print(f"🧪 Test query - all user_ids: {[r.get('user_id') for r in test_resp.data]}")

        # Get user preferences from database
        resp = supabase.table("user_job_preferences").select("*").eq("user_id", user_id).execute()

        print(f"📊 Supabase response: {resp}")
        print(f"📊 Response data: {resp.data}")
        print(f"📊 Response error: {getattr(resp, 'error', None)}")

        if not resp.data:
            print(f"❌ No preferences found for user: {user_id}")
            raise HTTPException(status_code=404, detail="User preferences not found")

        preferences = resp.data[0]
        print(f"🎯 Fetching personalized jobs for user: {user_id}")

        # Generate search requests based on user preferences
        search_requests = []

        # Create searches for each location and job type combination
        for location in preferences.get('preferred_locations', ['Remote'])[:3]:  # Limit to 3 locations
            for job_type in preferences.get('preferred_job_types', ['Full-time'])[:2]:  # Limit to 2 job types
                # Use industries as keywords if available
                if preferences.get('preferred_industries'):
                    for industry in preferences.get('preferred_industries', [])[:2]:  # Limit to 2 industries
                        search_requests.append(JobSearchRequest(
                            keywords=f"{industry} {job_type}",
                            location=location,
                            job_type=job_type,
                            experience_level=preferences.get('experience_level'),
                            min_salary=preferences.get('min_salary'),
                            max_salary=preferences.get('max_salary')
                        ))
                else:
                    search_requests.append(JobSearchRequest(
                        keywords=job_type,
                        location=location,
                        job_type=job_type,
                        experience_level=preferences.get('experience_level'),
                        min_salary=preferences.get('min_salary'),
                        max_salary=preferences.get('max_salary')
                    ))

        # Limit total searches to prevent API overuse
        search_requests = search_requests[:6]

        all_jobs = []

        # Execute searches
        for search_request in search_requests:
            try:
                result = await search_jobs_realtime(search_request)
                all_jobs.extend(result.get('jobs', []))

                # Add delay between API calls to respect rate limits
                await asyncio.sleep(0.5)
            except Exception as search_error:
                print(f"⚠️ Search failed for {search_request.keywords}: {search_error}")
                continue

        # Remove duplicates and limit results
        unique_jobs = remove_duplicate_jobs(all_jobs)
        final_jobs = unique_jobs[:limit]

        print(f"✅ Found {len(final_jobs)} personalized jobs for user")

        return {
            "jobs": final_jobs,
            "total_found": len(unique_jobs),
            "user_preferences": preferences,
            "searches_performed": len(search_requests)
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error fetching jobs for user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch personalized jobs: {str(e)}")

@router.get("/")
def list_jobs():
    """Get all jobs from the database with quality enhancement."""
    try:
        # Get page parameter for pagination, default to 1
        page_size = 500  # Increased to get more jobs from multiple sources
        
        print("Querying Supabase for jobs...")
        # Query with high limit to ensure enough jobs
        response = supabase.table("jobs").select("*").limit(page_size).execute()
        
        if hasattr(response, 'error') and response.error is not None:
            print(f"Error fetching jobs from Supabase: {response.error}")
            # Return test data if Supabase has an error
            return JSONResponse(content=filter_jobs_for_display(TEST_JOBS, strict_mode=False, enhance_missing=True, prioritize_real_logos=True))
        
        if not response.data or len(response.data) == 0:
            print("DEPRECATED: Static job storage removed. Returning test data only.")
            return JSONResponse(content=filter_jobs_for_display(TEST_JOBS, strict_mode=False, enhance_missing=True, prioritize_real_logos=True))
        
        # Filter jobs to ensure they have description and picture for display
        filtered_jobs = filter_jobs_for_display(response.data, strict_mode=False, enhance_missing=True, prioritize_real_logos=True)
        
        print(f"Retrieved {len(response.data)} jobs from Supabase, filtered to {len(filtered_jobs)} display-ready listings")
        return JSONResponse(content=filtered_jobs)
    except Exception as e:
        print(f"Exception in list_jobs: {str(e)}")
        # Fall back to test data if there's an exception
        return JSONResponse(content=filter_jobs_for_display(TEST_JOBS, strict_mode=False, enhance_missing=True, prioritize_real_logos=True))


def filter_quality_jobs(jobs_list):
    """Filter jobs to enhance quality, but ensure users always have jobs to view.
    Apply basic improvements to job listings when fields are missing.
    
    Args:
        jobs_list: List of job dictionaries
        
    Returns:
        List of enhanced jobs with reasonable quality
    """
    enhanced_jobs = []
    total_improved = 0
    
    for job in jobs_list:
        # Check and fix fields when possible
        needs_enhancement = False
        
        # Handle location
        if not job.get('location') or len(str(job.get('location', '')).strip()) == 0:
            job['location'] = job.get('job_location') or 'Remote/Flexible'
            needs_enhancement = True
        
        # Handle logo/image - copy from image to logo and vice versa if one is missing
        if not job.get('logo') or len(str(job.get('logo', '')).strip()) == 0:
            if job.get('image') and len(str(job.get('image', '')).strip()) > 0:
                job['logo'] = job['image']
                needs_enhancement = True
            else:
                # Use a nice default company logo as placeholder
                job['logo'] = f"https://ui-avatars.com/api/?name={job.get('company', 'Company')}&background=random&size=150"
                needs_enhancement = True
        
        # Handle description
        if not job.get('description') or len(str(job.get('description', '')).strip()) < 10:
            # Generate a basic description from available info
            company = job.get('company', 'A company')
            title = job.get('title', 'position')
            job['description'] = f"Join {company} as a {title}. This role offers an opportunity to work with a great team on exciting projects."
            needs_enhancement = True
            
        if needs_enhancement:
            total_improved += 1
            
        enhanced_jobs.append(job)
    
    print(f"Enhanced {total_improved} out of {len(jobs_list)} job listings to improve quality")
    return enhanced_jobs

@router.post("/applications")
def save_application(application_data: Dict[str, Any] = Body(...)):
    """Save a job application with 'applying' status"""
    try:
        # Generate a unique ID for the application
        application_id = str(uuid.uuid4())
        
        # Create application record with applying status
        application = {
            "id": application_id,
            "job_id": application_data.get("job_id"),
            "user_id": application_data.get("user_id"),
            "status": "applying",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "job_data": application_data.get("job_data", {})
        }
        
        # Store in database (mock - would use supabase in production)
        print(f"Saving application: {application}")
        
        # Return successful response with the application ID
        return {"id": application_id, "status": "applying"}
    except Exception as e:
        print(f"Error saving application: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/applications/{application_id}")
def update_application_status(
    application_id: str, 
    update_data: Dict[str, Any] = Body(...)
):
    """Update a job application status"""
    try:
        # Get the new status from request body
        new_status = update_data.get("status")
        if not new_status:
            raise HTTPException(status_code=400, detail="Status field is required")
            
        # In a real implementation, validate the application exists
        # and belongs to the requesting user
        
        # Update the application status
        updated_application = {
            "id": application_id,
            "status": new_status,
            "updated_at": datetime.now().isoformat()
        }
        
        print(f"Updating application {application_id} to status: {new_status}")
        
        # Return the updated application
        return updated_application
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error updating application: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/applications")
def list_applications(profile_id: str = None):
    """List applications for a specific user"""
    if not profile_id:
        raise HTTPException(status_code=400, detail="profile_id parameter is required")

    try:
        # Query the database for applications by this specific user
        resp = supabase.table("matches").select("""
            *,
            jobs:job_id (*)
        """).eq("profile_id", profile_id).order("created_at", desc=True).execute()

        if not resp.data:
            # Return empty list if no applications found for this user
            return []

        # Transform the data to match the expected format
        applications = []
        for match in resp.data:
            job_data = match.get("jobs") or {}
            application = {
                "id": match.get("id"),
                "job_id": match.get("job_id"),
                "profile_id": match.get("profile_id"),
                "status": match.get("status", "applying"),
                "created_at": match.get("created_at"),
                "updated_at": match.get("updated_at"),
                "job_data": {
                    "id": job_data.get("id"),
                    "title": job_data.get("title", "Unknown Job"),
                    "company": job_data.get("company", "Unknown Company"),
                    "location": job_data.get("location", "Unknown Location"),
                    "pay": job_data.get("salary"),
                    "description": job_data.get("description"),
                    "url": job_data.get("url")
                }
            }
            applications.append(application)

        return applications

    except Exception as e:
        print(f"Error fetching applications for user {profile_id}: {str(e)}")
        # Return empty list on error instead of failing
        return []
