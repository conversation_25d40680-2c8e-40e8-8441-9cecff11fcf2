// aiServices.ts
// Comprehensive AI Services Hub for Jobbify
// Includes: OpenAI, OpenRouter, DeepSeek, Job Evaluation, Resume Analysis, Cover Letter Generation

import axios from 'axios';
import * as FileSystem from 'expo-file-system';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface ProcessedJobDescription {
  cleanDescription: string;
  qualifications: string[];
  requirements: string[];
  keyHighlights: string[];
  summary: string;
  aiExplanation?: string;
}

export interface JobDetails {
  company: string;
  position: string;
  contactPerson?: string;
  keyRequirements?: string;
  userSkills?: string;
  companyInfo?: string;
}

export interface CoverLetterOptions {
  tone: 'professional' | 'conversational' | 'enthusiastic';
  style: 'traditional' | 'modern' | 'creative';
  focus: 'experience' | 'skills' | 'culture-fit';
}

export interface JobEvaluation {
  overallScore: number;
  summary: string;
  pros: string[];
  cons: string[];
  fitScore: number;
  salaryInsight: string;
  careerGrowth: string;
  workLifeBalance: string;
  companyReputation: string;
  recommendation: string;
  keyHighlights: string[];
  potentialConcerns: string[];
}

export interface ResumeFile {
  name: string;
  uri: string;
  size: number;
  mimeType: string;
  lastModified?: number;
}

export interface KeySkill {
  skill: string;
  score: number;
  evidence?: string;
}

export interface JobMatch {
  id: string;
  title: string;
  company: string;
  matchScore: number;
  missingSkills: string[];
}

export interface ResumeSection {
  sectionName: string;
  score: number;
  summary: string;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  examples?: string[];
}

export interface ResumeAnalysis {
  overallSummary: string;
  overallScore: number;
  strengths: string[];
  areasForImprovement: string[];
  sections: ResumeSection[];
  keySkills: KeySkill[];
  jobMatches: JobMatch[];
  visualSuggestions: string[];
}

// ============================================================================
// CONFIGURATION
// ============================================================================

const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const DEEPSEEK_MODEL = 'deepseek/deepseek-r1-0528:free';

const OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY || '';
const OPENROUTER_API_KEY = process.env.EXPO_PUBLIC_OPENROUTER_API_KEY || '';
const SITE_URL = process.env.EXPO_PUBLIC_SITE_URL || 'http://localhost';
const SITE_NAME = process.env.EXPO_PUBLIC_SITE_NAME || 'Jobbify';

const HAS_OPENAI_KEY = !!OPENAI_API_KEY;
const HAS_OPENROUTER_KEY = !!OPENROUTER_API_KEY;

// ============================================================================
// CORE AI FUNCTIONS
// ============================================================================

/**
 * Generic AI completion function that uses OpenAI if available, otherwise OpenRouter
 */
async function getAIResponse(messages: Array<{role: string, content: string}>, maxTokens: number = 256): Promise<string> {
  // Use OpenAI if key provided, otherwise fallback to OpenRouter DeepSeek
  if (!HAS_OPENAI_KEY) {
    try {
      // For OpenRouter, we need to format the messages into a single prompt
      const prompt = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
      return await getOpenRouterCompletion(prompt);
    } catch (e) {
      return 'AI service unavailable. Please try again later.';
    }
  }

  try {
    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages,
        max_tokens: maxTokens,
        temperature: 0.7,
      }),
    });
    if (!response.ok) {
      return `AI API error: ${response.statusText}`;
    }
    const data = await response.json();
    return data.choices?.[0]?.message?.content?.trim() || 'No answer from AI.';
  } catch (err) {
    return 'Failed to connect to AI service.';
  }
}

/**
 * OpenRouter completion function
 */
export const getOpenRouterCompletion = async (prompt: string): Promise<string> => {
  if (!OPENROUTER_API_KEY) {
    throw new Error('OpenRouter API key missing');
  }
  try {
    const response = await axios.post(
      OPENROUTER_API_URL,
      {
        model: DEEPSEEK_MODEL,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
      },
      {
        headers: {
          Authorization: `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': SITE_URL,
          'X-Title': SITE_NAME,
        },
      }
    );

    return response.data?.choices?.[0]?.message?.content ?? '';
  } catch (error: any) {
    const errorMessage = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('Error getting completion from OpenRouter:', errorMessage);
    throw new Error(`Failed to get completion from OpenRouter: ${errorMessage}`);
  }
};

/**
 * Basic AI completion for general queries
 */
export async function getAICompletion(userMessage: string): Promise<string> {
  return getAIResponse([
    { role: 'system', content: 'You are a helpful AI career assistant for job seekers. Answer concisely and with actionable advice.' },
    { role: 'user', content: userMessage },
  ], 256);
}

// ============================================================================
// JOB DESCRIPTION PROCESSING
// ============================================================================

/**
 * AI-powered job description processing
 */
export async function processJobDescriptionWithAI(
  rawDescription: string,
  jobTitle: string,
  company: string
): Promise<ProcessedJobDescription> {
  try {
    const prompt = `Please analyze this job description and provide a comprehensive, readable summary. Extract ALL important information without filtering out details.

Job Title: ${jobTitle}
Company: ${company}
Raw Description: ${rawDescription}

Please provide your response in the following JSON format:
{
  "cleanDescription": "A clean, readable paragraph describing the role and responsibilities without code syntax or formatting issues",
  "qualifications": ["List ALL qualifications, skills, and preferred experience mentioned"],
  "requirements": ["List ALL requirements, education, certifications, and mandatory criteria"],
  "keyHighlights": ["3-5 key selling points about this role that would attract candidates"],
  "summary": "A 2-3 sentence compelling summary of the role",
  "aiExplanation": "A friendly explanation of what this job involves and why someone might be interested"
}

Important: Respond ONLY with valid JSON. Do not include any other text.`;

    const response = await getAIResponse([
      { role: 'system', content: 'You are an expert job description processor. Always respond with valid JSON in the exact format requested.' },
      { role: 'user', content: prompt },
    ], 1500);

    // Try to parse the JSON response
    try {
      const parsed = JSON.parse(response);

      // Validate the response has required fields
      if (parsed.cleanDescription && parsed.qualifications && parsed.requirements &&
          parsed.keyHighlights && parsed.summary) {
        return {
          cleanDescription: parsed.cleanDescription,
          qualifications: Array.isArray(parsed.qualifications) ? parsed.qualifications : [],
          requirements: Array.isArray(parsed.requirements) ? parsed.requirements : [],
          keyHighlights: Array.isArray(parsed.keyHighlights) ? parsed.keyHighlights : [],
          summary: parsed.summary,
          aiExplanation: parsed.aiExplanation ||
            `Let me explain this ${jobTitle} role at ${company}. ` +
            `Based on the job description, this position involves ${parsed.cleanDescription.substring(0, 100)}... ` +
            `The most important skills seem to be related to ${parsed.qualifications.slice(0, 2).join(' and ')}. ` +
            `This could be a good fit if you enjoy these types of responsibilities and have experience in this field.`
        };
      }
    } catch (parseError) {
      console.warn('Failed to parse AI response as JSON:', parseError);
    }

    // Fallback: if AI response isn't valid JSON, create a basic processed version
    return createFallbackProcessedDescription(rawDescription, jobTitle, company);

  } catch (error) {
    console.error('Error processing job description with AI:', error);
    return createFallbackProcessedDescription(rawDescription, jobTitle, company);
  }
}

/**
 * Create fallback processed description when AI fails
 */
function createFallbackProcessedDescription(
  rawDescription: string,
  jobTitle: string,
  company: string
): ProcessedJobDescription {
  // Clean up the description
  const cleanDescription = rawDescription
    .replace(/[^\w\s.,!?()-]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Extract qualifications and requirements using keyword matching
  const qualificationKeywords = ['experience', 'skill', 'knowledge', 'proficiency', 'familiar', 'background'];
  const requirementKeywords = ['required', 'must', 'need', 'essential', 'mandatory', 'degree', 'certification'];

  const sentences = cleanDescription.split(/[.!?]+/).filter(s => s.trim().length > 10);
  const qualifications: string[] = [];
  const requirements: string[] = [];

  sentences.forEach(sentence => {
    const lowerSentence = sentence.toLowerCase();
    const cleanSentence = sentence.trim();

    const isQualification = qualificationKeywords.some(keyword =>
      lowerSentence.includes(keyword)
    );
    const isRequirement = requirementKeywords.some(keyword =>
      lowerSentence.includes(keyword)
    );

    if (isQualification && !qualifications.includes(cleanSentence)) {
      qualifications.push(cleanSentence);
    }
    if (isRequirement && !requirements.includes(cleanSentence)) {
      requirements.push(cleanSentence);
    }
  });

  // Provide fallbacks if nothing was extracted
  if (qualifications.length === 0) {
    qualifications.push(
      'Excellent communication skills',
      'Strong problem-solving abilities',
      'Team collaboration experience',
      'Attention to detail'
    );
  }

  if (requirements.length === 0) {
    requirements.push(
      'Bachelor\'s degree or equivalent experience',
      'Relevant work experience',
      'Proficiency with industry-standard tools'
    );
  }

  return {
    cleanDescription: cleanDescription || `Join ${company} as a ${jobTitle}. This role offers an opportunity to work with a great team on exciting projects.`,
    qualifications,
    requirements,
    keyHighlights: [
      `Work as a ${jobTitle} at ${company}`,
      'Opportunity for professional growth',
      'Collaborative team environment'
    ],
    summary: `Exciting ${jobTitle} opportunity at ${company}. Join a dynamic team and contribute to meaningful projects.`,
    aiExplanation: `This ${jobTitle} position at ${company} looks like a great opportunity to grow your career. The role involves working with a team on interesting projects and developing your skills in the field.`
  };
}

// ============================================================================
// RESUME ANALYSIS
// ============================================================================

/**
 * Get resume feedback using DeepSeek AI
 */
export async function getDeepSeekResumeFeedback(resumeText: string, siteUrl?: string, siteTitle?: string): Promise<string> {
  if (!OPENROUTER_API_KEY) {
    throw new Error('OpenRouter API key is not configured. Please check your .env file.');
  }
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
    'Content-Type': 'application/json',
    'HTTP-Referer': siteUrl || SITE_URL,
    'X-Title': siteTitle || SITE_NAME,
  };

  const body = {
    model: DEEPSEEK_MODEL,
    messages: [
      {
        role: 'user',
        content:
`You are a professional resume advisor. Analyze the following resume and provide actionable, detailed feedback in strict JSON format.

Instructions:
- Always provide a resume score between 60 and 100 (never zero).
- Always provide at least 3 strengths and 3 areas to improve, as bullet points.
- Always check for missing sections (like Education, Work Experience, Skills, Contact Info).
- For each main section in the resume (Education, Work Experience, Skills, etc.), provide a score out of 10, a short feedback summary, and 1-2 suggestions.
- Suggest 2-3 job matches based on the resume, with title, company, match percentage, location, salary, and missing skills.
- If you are unsure, make your best guess based on the content.
- Respond ONLY with valid JSON in the following format:

{
  "score": 85,
  "strengths": ["Strong technical skills", "Clear work history", "Good education background"],
  "improvements": ["Add more quantified achievements", "Include soft skills", "Update contact information"],
  "missingSections": ["Skills", "Certifications"],
  "sections": [
    {
      "name": "Work Experience",
      "score": 8,
      "feedback": "Good experience but needs more details",
      "suggestions": ["Add quantified achievements", "Include specific technologies used"]
    }
  ],
  "jobMatches": [
    {
      "title": "Software Engineer",
      "company": "TechCorp",
      "matchPercentage": 92,
      "location": "Remote",
      "salary": "$100,000",
      "missingSkills": ["Docker"]
    }
  ]
}

Resume:\n${resumeText}`,
      },
    ],
  };

  try {
    const response = await axios.post(OPENROUTER_API_URL, body, { headers, timeout: 180000 });
    if (!response.data?.choices) {
      console.error('Unexpected response format:', response.data);
      throw new Error('Failed to retrieve resume feedback');
    }
    if (response.status === 401) {
      throw new Error('Invalid API key. Please check your .env file.');
    }
    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('Error generating resume feedback:', error);
    throw error;
  }
}

// ============================================================================
// COVER LETTER GENERATION
// ============================================================================

/**
 * Generate AI cover letter using DeepSeek
 */
export async function generateAICoverLetter(
  jobDetails: JobDetails,
  options: CoverLetterOptions,
  resumeText?: string
): Promise<string> {
  if (!OPENROUTER_API_KEY) {
    throw new Error('OpenRouter API key is not configured. Please check your .env file.');
  }
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
    'Content-Type': 'application/json',
    'HTTP-Referer': SITE_URL,
    'X-Title': SITE_NAME,
  };

  // Clean empty fields to avoid "undefined" text in prompt
  const cleanJobDetails = Object.entries(jobDetails).reduce((acc, [key, value]) => {
    if (value && value.trim() !== '') {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  const prompt = `
You are a professional cover letter writer. Create a personalized cover letter for a job application
with the following details:

Company: ${cleanJobDetails.company || '[Company name not provided]'}
Position: ${cleanJobDetails.position || '[Position not provided]'}
${cleanJobDetails.contactPerson ? `Contact Person: ${cleanJobDetails.contactPerson}` : ''}
${cleanJobDetails.keyRequirements ? `Key Requirements: ${cleanJobDetails.keyRequirements}` : ''}
${cleanJobDetails.userSkills ? `Applicant Skills: ${cleanJobDetails.userSkills}` : ''}
${cleanJobDetails.companyInfo ? `Company Information: ${cleanJobDetails.companyInfo}` : ''}

Style preferences:
- Tone: ${options.tone}
- Style: ${options.style}
- Focus: ${options.focus}

The cover letter should:
1. Have a professional greeting (using the contact person's name if provided)
2. Include a compelling introduction that grabs attention
3. Highlight the applicant's relevant skills and experience that match the job requirements
4. Explain why they are interested in the company and position specifically
5. End with a strong call to action and professional closing
6. Be concise (around 300-400 words)
7. Be ready to use with minimal editing

${resumeText ? `\nResume for reference:\n${resumeText}` : ''}
`;

  const body = {
    model: DEEPSEEK_MODEL,
    messages: [
      {
        role: 'user',
        content: prompt,
      },
    ],
  };

  try {
    const response = await axios.post(OPENROUTER_API_URL, body, { headers, timeout: 180000 });
    if (!response.data?.choices) {
      console.error('Unexpected response format:', response.data);
      throw new Error('Failed to generate cover letter');
    }
    if (response.status === 401) {
      throw new Error('Invalid API key. Please check your .env file.');
    }
    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('Error generating cover letter:', error);
    return "I'm sorry, I couldn't generate a cover letter at this time. Please try again later.";
  }
}

// ============================================================================
// JOB EVALUATION SERVICE
// ============================================================================

class JobEvaluationService {
  /**
   * Evaluate a job description using AI
   */
  async evaluateJob(jobTitle: string, company: string, description: string, location: string = ''): Promise<JobEvaluation> {
    // If no API key is available, return fallback evaluation immediately
    if (!OPENROUTER_API_KEY) {
      console.warn('No OpenRouter API key available, using fallback evaluation');
      return this.getFallbackEvaluation(jobTitle, company);
    }

    try {
      const prompt = `Analyze this job posting and provide a comprehensive evaluation. Be specific and actionable.

Job Title: ${jobTitle}
Company: ${company}
Location: ${location}
Description: ${description}

Provide your analysis in the following JSON format:
{
  "overallScore": 85,
  "summary": "Brief 2-3 sentence summary of the role",
  "pros": ["List 3-5 positive aspects"],
  "cons": ["List 2-4 potential concerns"],
  "fitScore": 80,
  "salaryInsight": "Analysis of compensation expectations",
  "careerGrowth": "Assessment of growth opportunities",
  "workLifeBalance": "Evaluation of work-life balance indicators",
  "companyReputation": "Brief company assessment",
  "recommendation": "Clear recommendation (Apply/Consider/Skip) with reasoning",
  "keyHighlights": ["3-4 most attractive aspects"],
  "potentialConcerns": ["2-3 things to watch out for"]
}

Respond ONLY with valid JSON.`;

      const response = await axios.post(
        OPENROUTER_API_URL,
        {
          model: DEEPSEEK_MODEL,
          messages: [
            {
              role: 'system',
              content: 'You are an expert career advisor. Analyze job postings objectively and provide actionable insights. Always respond with valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        {
          headers: {
            'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': SITE_URL,
            'X-Title': SITE_NAME,
          },
          timeout: 30000
        }
      );

      if (response.data?.choices?.[0]?.message?.content) {
        try {
          const evaluation = JSON.parse(response.data.choices[0].message.content);

          // Validate required fields and provide defaults
          return {
            overallScore: Math.max(60, Math.min(100, evaluation.overallScore || 75)),
            summary: evaluation.summary || `${jobTitle} position at ${company}`,
            pros: Array.isArray(evaluation.pros) ? evaluation.pros : ['Opportunity for growth'],
            cons: Array.isArray(evaluation.cons) ? evaluation.cons : ['Limited information available'],
            fitScore: Math.max(60, Math.min(100, evaluation.fitScore || 75)),
            salaryInsight: evaluation.salaryInsight || 'Salary information not provided',
            careerGrowth: evaluation.careerGrowth || 'Growth potential to be determined',
            workLifeBalance: evaluation.workLifeBalance || 'Work-life balance details not specified',
            companyReputation: evaluation.companyReputation || 'Company reputation varies',
            recommendation: evaluation.recommendation || 'Consider applying based on your interests',
            keyHighlights: Array.isArray(evaluation.keyHighlights) ? evaluation.keyHighlights : ['Professional opportunity'],
            potentialConcerns: Array.isArray(evaluation.potentialConcerns) ? evaluation.potentialConcerns : ['Review job details carefully']
          };
        } catch (parseError) {
          console.warn('Failed to parse job evaluation JSON:', parseError);
          return this.getFallbackEvaluation(jobTitle, company);
        }
      }
    } catch (error) {
      console.error('Error evaluating job with AI:', error);
    }

    return this.getFallbackEvaluation(jobTitle, company);
  }

  /**
   * Format job evaluation for display
   */
  formatEvaluationForDisplay(evaluation: JobEvaluation): string {
    return `
**AI Job Evaluation (Score: ${evaluation.overallScore}/100)**

${evaluation.summary}

**Pros:**
${evaluation.pros.map(pro => `• ${pro}`).join('\n')}

**Potential Concerns:**
${evaluation.cons.map(con => `• ${con}`).join('\n')}

**Key Highlights:**
${evaluation.keyHighlights.map(highlight => `• ${highlight}`).join('\n')}

**Recommendation:** ${evaluation.recommendation}

**Career Growth:** ${evaluation.careerGrowth}

**Work-Life Balance:** ${evaluation.workLifeBalance}

**Salary Insight:** ${evaluation.salaryInsight}
    `.trim();
  }

  /**
   * Get fallback evaluation when AI is unavailable
   */
  private getFallbackEvaluation(jobTitle: string, company: string): JobEvaluation {
    return {
      overallScore: 75,
      summary: `${jobTitle} position at ${company}. This role offers professional growth opportunities in a dynamic environment.`,
      pros: [
        'Professional development opportunity',
        'Work with experienced team',
        'Gain valuable industry experience',
        'Contribute to meaningful projects'
      ],
      cons: [
        'Limited information available for detailed assessment',
        'Compensation details not specified'
      ],
      fitScore: 75,
      salaryInsight: 'Salary range not specified in job posting. Research market rates for similar positions.',
      careerGrowth: 'Potential for skill development and career advancement. Inquire about growth paths during interview.',
      workLifeBalance: 'Work-life balance details not provided. Ask about company culture and expectations.',
      companyReputation: `${company} appears to be actively hiring. Research company reviews and culture fit.`,
      recommendation: 'Consider applying if the role aligns with your career goals. Prepare questions about compensation and growth opportunities.',
      keyHighlights: [
        `${jobTitle} role at established company`,
        'Opportunity to develop professional skills',
        'Join a growing team',
        'Build relevant experience'
      ],
      potentialConcerns: [
        'Limited job description details',
        'Compensation not disclosed',
        'Company culture unknown'
      ]
    };
  }
}

// Create and export singleton instance
export const jobEvaluationService = new JobEvaluationService();

// ============================================================================
// RESUME ANALYSIS FUNCTIONS
// ============================================================================

/**
 * Simulate resume content extraction based on file type
 */
const extractResumeContent = async (file: ResumeFile): Promise<string> => {
  try {
    // In a real app, you would use different parsers based on file type
    // For PDF: pdf.js or react-native-pdf-lib
    // For DOCX: mammoth.js or similar
    // For this demo, we'll read the file as text if possible or simulate parsed content

    if (file.mimeType === 'text/plain') {
      // For text files, we can actually read the content
      const content = await FileSystem.readAsStringAsync(file.uri);
      return content;
    } else {
      // For other file types, in a real app you would send to a server
      // or use specialized libraries to extract text
      // Here we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 1500));
      return simulateResumeContent(file.name);
    }
  } catch (error) {
    console.error('Error extracting resume content:', error);
    return simulateResumeContent(file.name); // Fallback to mock data
  }
};

/**
 * Simulate resume content for demo purposes
 */
const simulateResumeContent = (fileName: string): string => {
  return `
John Doe
<EMAIL> | (555) 123-4567 | linkedin.com/in/johndoe

SUMMARY
Results-driven software engineer with 5+ years of experience in developing scalable web applications using React, Node.js, and TypeScript. Passionate about creating elegant solutions to complex problems.

SKILLS
• Programming Languages: JavaScript, TypeScript, Python, Java
• Frontend: React, Angular, Vue.js, HTML5, CSS3, SASS
• Backend: Node.js, Express, Django, Spring Boot
• Databases: MongoDB, PostgreSQL, MySQL, SQL Server, Oracle, Redis
• DevOps: Docker, Kubernetes, AWS, CI/CD, Git
• Tools: Webpack, Jest, Cypress, Postman

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | Jan 2020 - Present
• Led the development of a customer-facing portal that increased user engagement by 35%
• Architected and implemented a microservices-based backend that improved system reliability by 40%
• Mentored junior developers and conducted code reviews to ensure code quality
• Implemented CI/CD pipelines that reduced deployment time by 50%

Software Engineer | InnovateSoft | Mar 2017 - Dec 2019
• Developed and maintained multiple React applications used by over 10,000 daily users
• Built RESTful APIs using Node.js and Express, interfacing with various database systems
• Collaborated with UX designers to implement responsive and accessible user interfaces
• Optimized database queries resulting in a 30% performance improvement

EDUCATION

Bachelor of Science in Computer Science | University of Technology | 2013 - 2017
• GPA: 3.8/4.0
• Relevant coursework: Data Structures, Algorithms, Database Systems, Web Development

CERTIFICATIONS
• AWS Certified Developer - Associate
• MongoDB Certified Developer
• React Native Certified Developer
`;
};

/**
 * Add a utility function to escape RegExp special characters
 */
function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Analyze resume content
 */
const analyzeResume = async (content: string): Promise<ResumeAnalysis> => {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // --- Textual & Content Checks ---
  const hasEmail = /[\w.-]+@[\w.-]+\.[\w.-]+/g.test(content);
  const hasPhone = /\(\d{3}\)\s*\d{3}-\d{4}|\d{3}-\d{3}-\d{4}/g.test(content);
  const hasLinkedIn = /linkedin\.com\/in\//g.test(content);
  const hasSummary = /SUMMARY|OBJECTIVE|PROFILE/gi.test(content);
  const hasSkills = /SKILLS|TECHNOLOGIES|TECHNICAL SKILLS/gi.test(content);
  const hasExperience = /EXPERIENCE|WORK HISTORY|EMPLOYMENT/gi.test(content);
  const hasEducation = /EDUCATION|ACADEMIC|UNIVERSITY|COLLEGE/gi.test(content);

  // --- Design & Layout Checks ---
  const lineCount = content.split('\n').length;
  const wordCount = content.split(/\s+/).length;
  const avgLineLength = wordCount / Math.max(lineCount, 1);
  const hasBulletPoints = /\u2022|\*/.test(content) || /\n\s*\-/.test(content);
  const hasSections = /\n[A-Z][A-Z ]{3,}\n/.test(content);
  const hasConsistentIndent = !(content.match(/\n\s{5,}\S/g) || []).length;
  const hasTables = /\|.*\|/.test(content) || /Table|tabular/i.test(content);
  const hasExcessiveLength = wordCount > 900;
  const hasShortLength = wordCount < 150;

  // --- Skill Extraction ---
  const skillsList = [
    'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'Go',
    'React', 'Angular', 'Vue.js', 'Next.js', 'Svelte', 'Redux',
    'Node.js', 'Express', 'Django', 'Flask', 'Spring', 'ASP.NET',
    'MongoDB', 'PostgreSQL', 'MySQL', 'SQL Server', 'Oracle', 'Redis',
    'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'CI/CD',
    'REST API', 'GraphQL', 'Microservices', 'Serverless',
    'HTML', 'CSS', 'SASS', 'LESS', 'Bootstrap', 'Tailwind',
    'Git', 'Agile', 'Scrum', 'DevOps', 'TDD', 'BDD'
  ];
  const foundSkills: KeySkill[] = [];
  skillsList.forEach(skill => {
    const regex = new RegExp(escapeRegExp(skill), 'gi');
    if (regex.test(content)) {
      const score = 5 + Math.floor(Math.random() * 5); // Random score between 5-9
      foundSkills.push({
        skill,
        score,
        evidence: `Found ${skill} in resume content`
      });
    }
  });

  // --- Scoring ---
  let score = 0;
  // Contact info
  if (hasEmail) score += 5; else score -= 10;
  if (hasPhone) score += 5; else score -= 5;
  if (hasLinkedIn) score += 5;
  // Sections
  if (hasSummary) score += 10; else score -= 10;
  if (hasSkills) score += 15; else score -= 10;
  if (hasExperience) score += 20; else score -= 15;
  if (hasEducation) score += 10; else score -= 7;
  // Skills
  score += Math.min(foundSkills.length * 2, 10);
  // Design
  if (hasBulletPoints) score += 5; else score -= 5;
  if (hasSections) score += 5; else score -= 5;
  if (hasConsistentIndent) score += 3; else score -= 3;
  if (hasTables) score -= 2;
  // Layout
  if (hasExcessiveLength) score -= 15;
  if (hasShortLength) score -= 20;
  if (avgLineLength < 4) score -= 5;
  // Clamp score
  score = Math.max(0, Math.min(100, score));

  // --- Sections Feedback ---
  const sectionsFeedback: ResumeSection[] = [
    {
      sectionName: 'Contact Information',
      score: hasEmail && hasPhone ? 9 : (hasEmail || hasPhone ? 5 : 1),
      summary: hasEmail && hasPhone ? 'Good contact information' : 'Incomplete contact details',
      strengths: hasEmail && hasPhone ? ['Complete contact information'] : [],
      weaknesses: hasEmail && hasPhone ? [] : ['Missing email or phone number'],
      suggestions: hasEmail && hasPhone ? [] : ['Add complete contact information including email, phone, and LinkedIn'],
      examples: []
    },
    {
      sectionName: 'Professional Summary',
      score: hasSummary ? 8 : 2,
      summary: hasSummary ? 'Good summary present' : 'Missing professional summary',
      strengths: hasSummary ? ['Good summary'] : [],
      weaknesses: hasSummary ? [] : ['Missing summary'],
      suggestions: hasSummary ? ['Consider making your summary more achievement-oriented'] : ['Add a compelling professional summary'],
      examples: []
    },
    {
      sectionName: 'Skills Section',
      score: hasSkills ? 8 : 2,
      summary: hasSkills ? 'Skills section present' : 'Missing skills section',
      strengths: foundSkills.length > 10 ? ['Good variety of skills'] : [],
      weaknesses: foundSkills.length > 10 ? [] : ['Limited skills listed'],
      suggestions: foundSkills.length > 10 ? [] : ['Add more relevant skills with proficiency levels'],
      examples: []
    },
    {
      sectionName: 'Work Experience',
      score: hasExperience ? 9 : 2,
      summary: hasExperience ? 'Work experience section present' : 'Missing work experience',
      strengths: hasExperience ? ['Good work experience'] : [],
      weaknesses: hasExperience ? [] : ['Missing work experience'],
      suggestions: hasExperience ? ['Use more quantifiable achievements'] : ['Add detailed work experience with achievements'],
      examples: []
    },
    {
      sectionName: 'Education',
      score: hasEducation ? 7 : 2,
      summary: hasEducation ? 'Education section present' : 'Missing education details',
      strengths: hasEducation ? ['Good education background'] : [],
      weaknesses: hasEducation ? [] : ['Missing education details'],
      suggestions: hasEducation ? [] : ['Add your educational background'],
      examples: []
    },
    {
      sectionName: 'Design & Layout',
      score: hasBulletPoints && hasSections && hasConsistentIndent ? 9 : 3,
      summary: hasBulletPoints && hasSections && hasConsistentIndent ? 'Readable, well-structured layout' : 'Improve layout, use bullet points, clear sections, and consistent formatting',
      strengths: hasBulletPoints && hasSections && hasConsistentIndent ? ['Good layout'] : [],
      weaknesses: hasBulletPoints && hasSections && hasConsistentIndent ? [] : ['Poor layout'],
      suggestions: [
        ...(hasBulletPoints ? [] : ['Use bullet points for lists']),
        ...(hasSections ? [] : ['Use clear section headings']),
        ...(hasConsistentIndent ? [] : ['Use consistent indentation'])
      ],
      examples: []
    },
    {
      sectionName: 'Length',
      score: hasExcessiveLength ? 2 : (hasShortLength ? 1 : 8),
      summary: hasExcessiveLength ? 'Resume is too long' : (hasShortLength ? 'Resume is too short' : 'Good resume length'),
      strengths: hasExcessiveLength ? [] : (hasShortLength ? [] : ['Good length']),
      weaknesses: hasExcessiveLength ? ['Too long'] : (hasShortLength ? ['Too short'] : []),
      suggestions: [
        ...(hasExcessiveLength ? ['Reduce length to 1-2 pages'] : []),
        ...(hasShortLength ? ['Add more content, elaborate on your experience'] : [])
      ],
      examples: []
    }
  ];

  // --- Missing Sections ---
  const missingSections: string[] = [];
  if (!hasSummary) missingSections.push('Professional Summary');
  if (!hasSkills) missingSections.push('Skills Section');
  if (!hasExperience) missingSections.push('Work Experience');
  if (!hasEducation) missingSections.push('Education');
  if (!hasLinkedIn) missingSections.push('LinkedIn Profile');

  // --- Improvement Suggestions ---
  const improvement: string[] = [
    'Add more quantifiable achievements with metrics',
    'Tailor your resume to each job application',
    'Use action verbs to start bullet points',
    'Keep resume concise and focused on relevant experience',
    'Ensure consistent formatting throughout',
    ...(hasShortLength ? ['Expand your resume with more details and context'] : []),
    ...(hasExcessiveLength ? ['Trim unnecessary content and focus on relevance'] : [])
  ];

  // --- Strengths ---
  const strengths: string[] = [];
  if (score > 80) strengths.push('Excellent structure and content');
  if (score > 65) strengths.push('Well-structured resume');
  if (hasSkills && foundSkills.length > 8) strengths.push('Comprehensive skills section');
  if (hasExperience) strengths.push('Detailed work experience');
  if (hasEducation) strengths.push('Good education background');
  if (hasBulletPoints && hasSections && hasConsistentIndent) strengths.push('Professional layout and formatting');

  // --- Job Matches ---
  const jobMatches = generateJobMatches(foundSkills.map(s => s.skill));

  return {
    overallSummary: 'Resume analysis summary',
    overallScore: score,
    strengths,
    areasForImprovement: missingSections,
    sections: sectionsFeedback,
    keySkills: foundSkills,
    jobMatches,
    visualSuggestions: []
  };
};

/**
 * Generate job matches based on skills
 */
const generateJobMatches = (skills: string[]): JobMatch[] => {
  const jobs: JobMatch[] = [
    {
      id: '1',
      title: 'Frontend Developer',
      company: 'TechStart Inc.',
      matchScore: 0,
      missingSkills: ['React', 'TypeScript', 'CSS']
    },
    {
      id: '2',
      title: 'Full Stack Engineer',
      company: 'GrowthTech',
      matchScore: 0,
      missingSkills: ['Node.js', 'React', 'MongoDB']
    },
    {
      id: '3',
      title: 'Backend Developer',
      company: 'DataFlow Systems',
      matchScore: 0,
      missingSkills: ['Node.js', 'Express', 'PostgreSQL']
    },
    {
      id: '4',
      title: 'DevOps Engineer',
      company: 'CloudScale',
      matchScore: 0,
      missingSkills: ['Docker', 'Kubernetes', 'AWS']
    },
    {
      id: '5',
      title: 'Mobile Developer',
      company: 'AppWorks',
      matchScore: 0,
      missingSkills: ['React Native', 'TypeScript', 'Redux']
    }
  ];

  // Calculate match score based on skills
  return jobs.map(job => {
    const requiredSkills = [...job.missingSkills];
    const matchedSkills = requiredSkills.filter(skill =>
      skills.some(userSkill => userSkill.toLowerCase() === skill.toLowerCase())
    );

    // Update missing skills based on what user has
    const updatedMissingSkills = requiredSkills.filter(skill =>
      !skills.some(userSkill => userSkill.toLowerCase() === skill.toLowerCase())
    );

    // Calculate match score
    const matchScore = Math.min(
      100,
      Math.floor((matchedSkills.length / requiredSkills.length) * 100) +
      Math.floor(Math.random() * 30) // Add some randomness for demo
    );

    return {
      ...job,
      matchScore,
      missingSkills: updatedMissingSkills
    };
  }).sort((a, b) => b.matchScore - a.matchScore); // Sort by match score
};

/**
 * Main function to process a resume file
 */
export const processResume = async (file: ResumeFile): Promise<ResumeAnalysis> => {
  try {
    const content = await extractResumeContent(file);
    const analysis = await analyzeResume(content);
    return analysis;
  } catch (error) {
    console.error('Error processing resume:', error);
    throw new Error('Failed to process resume');
  }
};
