"""
Consolidated Job API Services for Jobbify Backend
Includes <PERSON>by, <PERSON><PERSON><PERSON>, and The Muse API integrations
"""

import os
import asyncio
import httpx
import json
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

# ============================================================================
# API CONFIGURATION
# ============================================================================

# Ashby API configuration
ASHBY_JOB_BOARD_NAME = os.getenv("ASHBY_JOB_BOARD_NAME", "ashby")
ASHBY_BASE_URL = "https://api.ashbyhq.com/posting-api"

# Jooble API configuration
JOOBLE_API_KEY = "9908d703-d6b5-4349-a712-6b5f01483150"
JOOBLE_HOST = "jooble.org"
JOOBLE_ENDPOINT = f"/api/{JOOBLE_API_KEY}"

# The Muse API configuration
MUSE_API_KEY = "39a8dde5f23991b29f8097f3638271072d9a749c44ee7f08de7f5b7e404b72d2"
MUSE_BASE_URL = "https://www.themuse.com/api/public"

# ============================================================================
# ASHBY API SERVICE
# ============================================================================

async def ashby(client: httpx.AsyncClient, job_board_name: str = None) -> List[Dict[str, Any]]:
    """Fetch jobs from Ashby API"""
    try:
        board_name = job_board_name or ASHBY_JOB_BOARD_NAME
        print(f"🔄 Fetching from Ashby API for job board: {board_name}...")
        
        headers = {
            "User-Agent": "JobbifyBot/1.0 (+https://jobbify.app)",
            "Accept": "application/json"
        }
        
        params = {
            "includeCompensation": "true"
        }
        
        url = f"{ASHBY_BASE_URL}/job-board/{board_name}"
        
        r = await client.get(url, 
                            params=params,
                            headers=headers, 
                            timeout=20)
        r.raise_for_status()
        
        data = r.json()
        jobs = data.get("jobs", [])
        
        # Add source and external_id for each job
        for job in jobs:
            job["source"] = "ashby"
            job["external_id"] = job.get("jobUrl", "").split("/")[-1] if job.get("jobUrl") else ""
            
        print(f"✅ Ashby: Found {len(jobs)} jobs")
        return jobs
    except Exception as e:
        print(f"❌ Ashby error: {e}")
        return []

def map_ashby_job(j: Dict[str, Any]) -> Dict[str, Any]:
    """Map Ashby job data to standardized format"""
    try:
        # Extract location information
        location = j.get("location", "Remote")
        if j.get("isRemote"):
            location = "Remote"
        elif j.get("address", {}).get("postalAddress"):
            postal = j["address"]["postalAddress"]
            city = postal.get("addressLocality", "")
            region = postal.get("addressRegion", "")
            if city and region:
                location = f"{city}, {region}"
            elif city:
                location = city
        
        # Extract salary information
        salary = "Competitive"
        if j.get("compensation", {}).get("components"):
            components = j["compensation"]["components"]
            salary_components = [c for c in components if c.get("compensationType") == "Salary"]
            if salary_components:
                comp = salary_components[0]
                if comp.get("minValue") and comp.get("maxValue"):
                    currency = comp.get("currencyCode", "USD")
                    salary = f"{currency} {comp['minValue']:,} - {comp['maxValue']:,}"
        
        # Extract company logo (Ashby doesn't provide this directly, so we'll generate one)
        company_name = j.get("department", "Company")
        logo = f"https://ui-avatars.com/api/?name={company_name}&background=random&size=150"
        
        payload = {
            "title": j.get("title", ""),
            "company": company_name,  # Using department as company for now
            "location": location,
            "salary": salary,
            "logo": logo,
            "apply_url": j.get("applyUrl", ""),
            "description": j.get("descriptionPlain", "")[:1000],  # Use plain text description
            "source": "ashby",
            "external_id": j.get("jobUrl", "").split("/")[-1] if j.get("jobUrl") else "",
        }
        return payload
    except Exception as e:
        print(f"Error mapping Ashby job: {e}")
        return {
            "title": j.get("title", "Unknown Job"),
            "company": j.get("department", "Unknown Company"),
            "description": "Ashby job data could not be properly parsed.",
            "source": "ashby",
            "external_id": j.get("jobUrl", "").split("/")[-1] if j.get("jobUrl") else "",
        }

# ============================================================================
# JOOBLE API SERVICE
# ============================================================================

async def jooble(client: httpx.AsyncClient, keywords: str = "developer", location: str = "Remote") -> List[Dict[str, Any]]:
    """Fetch jobs from Jooble API"""
    try:
        print(f"🔄 Fetching from Jooble API with keywords: {keywords}, location: {location}...")
        
        # Jooble API request body
        body = {
            "keywords": keywords,
            "location": location
        }
        
        headers = {"Content-type": "application/json"}
        
        url = f"https://{JOOBLE_HOST}{JOOBLE_ENDPOINT}"
        
        r = await client.post(url, 
                             json=body, 
                             headers=headers, 
                             timeout=20)
        r.raise_for_status()
        
        data = r.json()
        jobs = data.get("jobs", [])
        
        # Add source and external_id for each job
        for job in jobs:
            job["source"] = "jooble"
            job["external_id"] = str(job.get("id", ""))
            
        print(f"✅ Jooble: Found {len(jobs)} jobs")
        return jobs
    except Exception as e:
        print(f"❌ Jooble error: {e}")
        return []

def map_jooble_job(j: Dict[str, Any]) -> Dict[str, Any]:
    """Map Jooble job data to standardized format"""
    try:
        payload = {
            "title": j.get("title", ""),
            "company": j.get("company", ""),
            "location": j.get("location", "Remote"),
            "salary": j.get("salary", "Competitive"),
            "logo": j.get("logo", ""),
            "apply_url": j.get("link", ""),
            "description": j.get("snippet", "")[:1000],  # Jooble uses 'snippet' for description
            "source": "jooble",
            "external_id": str(j.get("id", "")),
        }
        return payload
    except Exception as e:
        print(f"Error mapping Jooble job: {e}")
        return {
            "title": j.get("title", "Unknown Job"),
            "company": j.get("company", "Unknown Company"),
            "description": "Jooble job data could not be properly parsed.",
            "source": "jooble",
            "external_id": str(j.get("id", "")),
        }

# ============================================================================
# THE MUSE API SERVICE
# ============================================================================

async def muse(client: httpx.AsyncClient, category: str = "Computer and IT", location: str = "Flexible / Remote") -> List[Dict[str, Any]]:
    """Fetch jobs from The Muse API"""
    try:
        print(f"🔄 Fetching from The Muse API with category: {category}, location: {location}...")
        
        headers = {
            "User-Agent": "JobbifyBot/1.0 (+https://jobbify.app)",
            "X-API-Key": MUSE_API_KEY
        }
        
        params = {
            "category": category,
            "location": location,
            "page": 0,
            "descending": "true",
            "api_key": MUSE_API_KEY
        }
        
        url = f"{MUSE_BASE_URL}/jobs"
        
        r = await client.get(url, 
                            params=params,
                            headers=headers, 
                            timeout=20)
        r.raise_for_status()
        
        data = r.json()
        jobs = data.get("results", [])
        
        # Add source and external_id for each job
        for job in jobs:
            job["source"] = "muse"
            job["external_id"] = str(job.get("id", ""))
            
        print(f"✅ The Muse: Found {len(jobs)} jobs")
        return jobs
    except Exception as e:
        print(f"❌ The Muse error: {e}")
        return []

def map_muse_job(j: Dict[str, Any]) -> Dict[str, Any]:
    """Map The Muse job data to standardized format"""
    try:
        # Extract company information
        company_info = j.get("company", {})
        company_name = company_info.get("name", "Unknown Company")
        
        # Extract location information
        locations = j.get("locations", [])
        location = "Remote"
        if locations and len(locations) > 0:
            location = locations[0].get("name", "Remote")
        
        # Extract categories for tags
        categories = j.get("categories", [])
        category_names = [cat.get("name", "") for cat in categories if cat.get("name")]
        
        payload = {
            "title": j.get("name", ""),
            "company": company_name,
            "location": location,
            "salary": "Competitive",  # The Muse doesn't always provide salary info
            "logo": company_info.get("logo", ""),
            "apply_url": j.get("refs", {}).get("landing_page", ""),
            "description": j.get("contents", "")[:1000],  # The Muse uses 'contents' for description
            "source": "muse",
            "external_id": str(j.get("id", "")),
        }
        return payload
    except Exception as e:
        print(f"Error mapping The Muse job: {e}")
        return {
            "title": j.get("name", "Unknown Job"),
            "company": "Unknown Company",
            "description": "The Muse job data could not be properly parsed.",
            "source": "muse",
            "external_id": str(j.get("id", "")),
        }

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def standardize_job_data(job: Dict[str, Any], source: str) -> Dict[str, Any]:
    """Standardize job data format across different APIs"""
    standardized = {
        "title": job.get("title", ""),
        "company": job.get("company", ""),
        "location": job.get("location", "Remote"),
        "salary": job.get("salary", "Competitive"),
        "logo": job.get("logo", ""),
        "apply_url": job.get("apply_url", ""),
        "description": job.get("description", ""),
        "source": source,
        "external_id": job.get("external_id", ""),
        "posted_date": job.get("posted_date", ""),
        "employment_type": job.get("employment_type", "Full-time"),
        "remote": job.get("remote", False),
        "tags": job.get("tags", [])
    }
    
    # Ensure logo is not empty
    if not standardized["logo"] or standardized["logo"].strip() == "":
        company_name = standardized["company"].replace(" ", "+")
        standardized["logo"] = f"https://ui-avatars.com/api/?name={company_name}&background=random&size=150"
    
    return standardized

async def fetch_jobs_from_all_apis(keywords: str = "software developer", location: str = "Remote") -> List[Dict[str, Any]]:
    """Fetch jobs from all available APIs concurrently"""
    async with httpx.AsyncClient() as client:
        try:
            # Fetch from all APIs concurrently
            results = await asyncio.gather(
                jooble(client, keywords, location),
                muse(client, "Computer and IT", location),
                ashby(client),
                return_exceptions=True
            )
            
            all_jobs = []
            
            # Process Jooble results
            if isinstance(results[0], list):
                jooble_jobs = [map_jooble_job(job) for job in results[0]]
                all_jobs.extend(jooble_jobs)
            
            # Process Muse results
            if isinstance(results[1], list):
                muse_jobs = [map_muse_job(job) for job in results[1]]
                all_jobs.extend(muse_jobs)
            
            # Process Ashby results
            if isinstance(results[2], list):
                ashby_jobs = [map_ashby_job(job) for job in results[2]]
                all_jobs.extend(ashby_jobs)
            
            print(f"🎯 Total jobs fetched from all APIs: {len(all_jobs)}")
            return all_jobs
            
        except Exception as e:
            logger.error(f"Error fetching jobs from APIs: {e}")
            return []
