// jobApiServices.ts
// Complete Job API Integration Hub for Jobbify
// Includes: Ashby, RapidAPI, RemoteOK, Real-time job services, Caching, and Personalized Search

import { Job } from '@/context/AppContext';
import { processJobDescriptionWithAI, jobEvaluationService, JobEvaluation } from './aiServices';
import { supabase } from '@/lib/supabase';
import axios from 'axios';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

// Ashby API Response Interfaces
interface AshbyAddress {
  addressLocality?: string;
  addressRegion?: string;
  addressCountry?: string;
}

interface AshbySecondaryLocation {
  location: string;
  address: AshbyAddress;
}

interface AshbyCompensationComponent {
  id: string;
  summary: string;
  compensationType: 'Salary' | 'EquityPercentage' | 'Bonus' | 'Commission' | 'Other';
  interval: string;
  currencyCode?: string;
  minValue?: number;
  maxValue?: number;
}

interface AshbyCompensation {
  components: AshbyCompensationComponent[];
}

interface AshbyJob {
  title: string;
  location: string;
  secondaryLocations?: AshbySecondaryLocation[];
  department?: string;
  team?: string;
  isListed: boolean;
  isRemote: boolean;
  descriptionHtml: string;
  descriptionPlain: string;
  publishedAt: string;
  employmentType: 'FullTime' | 'PartTime' | 'Intern' | 'Contract' | 'Temporary';
  address?: {
    postalAddress: AshbyAddress;
  };
  jobUrl: string;
  applyUrl: string;
  compensation?: AshbyCompensation;
}

interface AshbyApiResponse {
  apiVersion: string;
  jobs: AshbyJob[];
}

// Real-time job service interfaces
export interface UserJobPreferences {
  user_id: string;
  preferred_locations: string[];
  preferred_job_types: string[];
  preferred_industries: string[];
  experience_level: string;
  min_salary: number;
  max_salary: number;
  remote_work_preference: string;
}

export interface JobSearchParams {
  keywords: string;
  location: string;
  jobType?: string;
  experienceLevel?: string;
  minSalary?: number;
  maxSalary?: number;
}

export interface CachedJobSearch {
  id: string;
  search_params: JobSearchParams;
  results: any[];
  created_at: string;
  expires_at: string;
  user_count: number;
}

interface ApiJob {
  job_title: string;
  company_name: string | { display_name?: string; name?: string };
  job_location: string;
  job_description: string;
  job_salary_range?: string;
  job_id: string;
  job_posted_date?: string;
  job_apply_link?: string;
  job_category?: string;
  job_type?: string;
  job_employment_type?: string;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

// Ashby API
const ASHBY_API_BASE_URL = 'https://api.ashbyhq.com/posting-api/job-board';

// RapidAPI
const RAPIDAPI_KEY = '**************************************************';
const RAPIDAPI_HOST = 'jsearch.p.rapidapi.com';

// RemoteOK API
const REMOTEOK_API_URL = 'https://remoteok.io/api';

// API URLs for different environments
const API_URLS = {
  networkIp: 'http://*************:8000',
  androidEmulator: 'http://********:8000',
  localApi: 'http://127.0.0.1:8000',
  localhost: 'http://localhost:8000'
};

// Real-time job service configuration
const CACHE_DURATION_HOURS = 2;

// ============================================================================
// ASHBY JOBS SERVICE
// ============================================================================

/**
 * Fetch jobs from Ashby API
 * @param jobBoardName - The Ashby job board name (e.g., "Ashby", "YourCompany")
 * @param includeCompensation - Whether to include compensation data
 * @returns Promise<Job[]> - Array of jobs in app format
 */
export const fetchAshbyJobs = async (
  jobBoardName: string,
  includeCompensation: boolean = true
): Promise<Job[]> => {
  try {
    console.log(`Fetching jobs from Ashby job board: ${jobBoardName}`);

    // Construct API URL
    const url = `${ASHBY_API_BASE_URL}/${jobBoardName}${includeCompensation ? '?includeCompensation=true' : ''}`;

    // Add cache-busting parameter to ensure fresh data
    const cacheBuster = new Date().getTime();
    const finalUrl = `${url}${includeCompensation ? '&' : '?'}_cb=${cacheBuster}`;

    const response = await fetch(finalUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      },
    });

    if (!response.ok) {
      throw new Error(`Ashby API error: ${response.status} ${response.statusText}`);
    }

    const data: AshbyApiResponse = await response.json();

    if (!data.jobs || !Array.isArray(data.jobs)) {
      console.warn('No jobs found in Ashby API response');
      return [];
    }

    // Filter and map jobs
    const mappedJobs = await mapAshbyJobsToAppJobs(data.jobs);
    console.log(`Successfully fetched and mapped ${mappedJobs.length} jobs from Ashby`);

    return mappedJobs;
  } catch (error) {
    console.error('Error fetching jobs from Ashby API:', error);
    return [];
  }
};

/**
 * Map Ashby jobs to app Job interface
 * Filters out unlisted jobs and jobs without required data
 */
const mapAshbyJobsToAppJobs = async (ashbyJobs: AshbyJob[]): Promise<Job[]> => {
  // Filter jobs to only include listed ones with required data
  const validJobs = ashbyJobs.filter((job) => {
    return (
      job.isListed &&
      job.title &&
      job.descriptionPlain &&
      job.applyUrl &&
      job.publishedAt
    );
  });

  // Process jobs with AI enhancement
  const processedJobs = await Promise.all(validJobs.map(async (ashbyJob) => {
    // Generate unique ID for the job
    const jobId = `ashby-${ashbyJob.jobUrl.split('/').pop() || Date.now()}`;

    // Extract company name from job URL or use a default
    const company = extractCompanyFromUrl(ashbyJob.jobUrl) || 'Company';

    // Format location
    const location = formatLocation(ashbyJob);

    // Format compensation
    const pay = formatCompensation(ashbyJob.compensation);

    // Generate tags from job data
    const tags = generateTags(ashbyJob);

    // Process job description with AI for better quality and readability
    const processedDescription = await processJobDescriptionWithAI(
      ashbyJob.descriptionPlain || '',
      ashbyJob.title,
      company
    );

    // Get AI evaluation
    const evaluation = await jobEvaluationService.evaluateJob(
      ashbyJob.title,
      company,
      ashbyJob.descriptionPlain,
      location
    );

    const job: Job = {
      id: jobId,
      title: ashbyJob.title,
      company: company,
      location: location,
      description: processedDescription.cleanDescription,
      pay: pay,
      logo: generateCompanyLogo(company),
      tags: tags,
      applyUrl: ashbyJob.applyUrl,
      postedDate: ashbyJob.publishedAt,
      source: 'Ashby',
      employmentType: mapEmploymentType(ashbyJob.employmentType),
      isRemote: ashbyJob.isRemote,
      department: ashbyJob.department,
      team: ashbyJob.team,
      qualifications: processedDescription.qualifications,
      requirements: processedDescription.requirements,
      keyHighlights: processedDescription.keyHighlights,
      summary: processedDescription.summary,
      aiExplanation: processedDescription.aiExplanation,
      evaluation: evaluation
    };

    return job;
  }));

  return processedJobs;
};

// ============================================================================
// RAPIDAPI JOBS SERVICE
// ============================================================================

/**
 * Fetch job search results from RapidAPI
 * Strictly filters for jobs posted within the last week
 */
export async function fetchJobSearch({ query, location, location_type = 'ANY', num_pages = 1 }: {
  query: string;
  location?: string;
  location_type?: string;
  num_pages?: number;
}) {
  // Corrected endpoint according to RapidAPI docs
  const url = 'https://jsearch.p.rapidapi.com/search';
  
  // Add cache busting and date filter parameter
  const cacheBuster = new Date().getTime();
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  
  // Format date for API: YYYY-MM-DD
  const dateStr = oneWeekAgo.toISOString().split('T')[0];
  
  const params = {
    query,
    location: location || '',
    location_type,
    num_pages,
    date_posted: `${dateStr}`,  // Only get jobs posted since this date
    _cb: cacheBuster.toString(), // Cache busting
  };
  
  const response = await axios.get(url, {
    params,
    headers: {
      'X-RapidAPI-Key': RAPIDAPI_KEY,
      'X-RapidAPI-Host': RAPIDAPI_HOST,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
  });

  // Filter jobs to only include those from the last week
  if (response.data && response.data.data) {
    const oneWeekAgoTimestamp = oneWeekAgo.getTime();
    response.data.data = response.data.data.filter((job: any) => {
      if (job.job_posted_at_datetime_utc) {
        const jobDate = new Date(job.job_posted_at_datetime_utc);
        return jobDate.getTime() >= oneWeekAgoTimestamp;
      }
      return true; // Include jobs without date info
    });
  }
  
  return response.data;
}

/**
 * Fetch job salary info from RapidAPI
 */
export async function fetchJobSalary({ jobTitle, location, yearsOfExperience = 0 }: {
  jobTitle: string;
  location?: string;
  yearsOfExperience?: number;
}) {
  const url = 'https://search.p.rapidapi.com/getdata/salary';
  const params = {
    jobTitle,
    location: location || '',
    years_of_experience: yearsOfExperience.toString(),
  };
  const response = await axios.get(url, {
    params,
    headers: {
      'X-RapidAPI-Key': RAPIDAPI_KEY,
      'X-RapidAPI-Host': 'search.p.rapidapi.com',
    },
  });
  return response.data;
}

// ============================================================================
// REAL-TIME JOB SERVICE
// ============================================================================

/**
 * Generate cache key for job search parameters
 */
function generateCacheKey(params: JobSearchParams): string {
  return `${params.keywords}-${params.location}-${params.jobType || 'any'}-${params.experienceLevel || 'any'}`;
}

/**
 * Get cached job search results
 */
export async function getCachedJobResults(params: JobSearchParams): Promise<any[] | null> {
  try {
    const cacheKey = generateCacheKey(params);
    const now = new Date();

    const { data, error } = await supabase
      .from('cached_job_searches')
      .select('*')
      .eq('cache_key', cacheKey)
      .gt('expires_at', now.toISOString())
      .single();

    if (error || !data) {
      return null;
    }

    console.log(`💾 Cache hit for: ${cacheKey}`);
    return data.results;
  } catch (error) {
    console.error('Error getting cached results:', error);
    return null;
  }
}

/**
 * Cache job search results
 */
export async function cacheJobResults(params: JobSearchParams, results: any[]): Promise<void> {
  try {
    const cacheKey = generateCacheKey(params);
    const now = new Date();
    const expiresAt = new Date(now.getTime() + CACHE_DURATION_HOURS * 60 * 60 * 1000);

    const { error } = await supabase
      .from('cached_job_searches')
      .upsert({
        cache_key: cacheKey,
        search_params: params,
        results: results,
        created_at: now.toISOString(),
        expires_at: expiresAt.toISOString(),
        user_count: 1
      });

    if (error) {
      console.error('Error caching results:', error);
    } else {
      console.log(`💾 Cached ${results.length} jobs for: ${cacheKey}`);
    }
  } catch (error) {
    console.error('Error caching job results:', error);
  }
}

/**
 * Get personalized job recommendations based on user preferences
 */
export async function getPersonalizedJobRecommendations(userId: string): Promise<any[]> {
  try {
    // Get user preferences
    const { data: preferences, error: prefError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (prefError || !preferences) {
      console.log('No user preferences found, using default search');
      return [];
    }

    const allJobs: any[] = [];

    // Generate search combinations based on user preferences
    const searchCombinations: JobSearchParams[] = [];

    for (const location of preferences.preferred_locations || ['Remote']) {
      for (const jobType of preferences.preferred_job_types || ['Software Engineer']) {
        searchCombinations.push({
          keywords: jobType,
          location: location,
          jobType: jobType,
          experienceLevel: preferences.experience_level,
          minSalary: preferences.min_salary,
          maxSalary: preferences.max_salary
        });
      }
    }

    // Process each search combination
    for (const searchParams of searchCombinations) {
      // Check cache first
      const cachedResults = await getCachedJobResults(searchParams);

      if (cachedResults) {
        allJobs.push(...cachedResults);
        continue;
      }

      // If not cached, fetch from APIs
      const freshResults = await fetchJobsFromAPIs(searchParams);

      if (freshResults.length > 0) {
        // Cache the results
        await cacheJobResults(searchParams, freshResults);
        allJobs.push(...freshResults);
      }

      // Add delay to respect API rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Remove duplicates and sort by relevance
    const uniqueJobs = removeDuplicateJobs(allJobs);
    const sortedJobs = sortJobsByRelevance(uniqueJobs, preferences);

    return sortedJobs.slice(0, 50); // Return top 50 jobs
  } catch (error) {
    console.error('Error getting personalized recommendations:', error);
    return [];
  }
}

/**
 * Fetch jobs from multiple APIs
 */
async function fetchJobsFromAPIs(params: JobSearchParams): Promise<any[]> {
  const jobs: any[] = [];

  try {
    console.log(`🔄 Fetching fresh jobs for: ${params.keywords} in ${params.location}`);

    // Call your FastAPI backend
    const response = await fetch('http://localhost:8000/jobs/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keywords: params.keywords,
        location: params.location,
        job_type: params.jobType,
        experience_level: params.experienceLevel,
        min_salary: params.minSalary,
        max_salary: params.maxSalary
      }),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.jobs && Array.isArray(data.jobs)) {
        jobs.push(...data.jobs);
      }
    }
  } catch (error) {
    console.error('Error fetching from APIs:', error);
  }

  return jobs;
}

/**
 * Remove duplicate jobs based on title and company
 */
function removeDuplicateJobs(jobs: any[]): any[] {
  const seen = new Set();
  return jobs.filter(job => {
    const key = `${job.title}-${job.company}`.toLowerCase();
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

/**
 * Sort jobs by relevance based on user preferences
 */
function sortJobsByRelevance(jobs: any[], preferences: UserJobPreferences): any[] {
  return jobs.sort((a, b) => {
    let scoreA = 0;
    let scoreB = 0;

    // Score based on job type preference
    if (preferences.preferred_job_types?.some(type =>
      a.title?.toLowerCase().includes(type.toLowerCase()))) {
      scoreA += 10;
    }
    if (preferences.preferred_job_types?.some(type =>
      b.title?.toLowerCase().includes(type.toLowerCase()))) {
      scoreB += 10;
    }

    // Score based on location preference
    if (preferences.preferred_locations?.some(loc =>
      a.location?.toLowerCase().includes(loc.toLowerCase()))) {
      scoreA += 5;
    }
    if (preferences.preferred_locations?.some(loc =>
      b.location?.toLowerCase().includes(loc.toLowerCase()))) {
      scoreB += 5;
    }

    // Score based on salary (if available)
    if (a.salary && preferences.min_salary) {
      const salaryA = extractSalaryNumber(a.salary);
      if (salaryA >= preferences.min_salary) {
        scoreA += 3;
      }
    }
    if (b.salary && preferences.min_salary) {
      const salaryB = extractSalaryNumber(b.salary);
      if (salaryB >= preferences.min_salary) {
        scoreB += 3;
      }
    }

    return scoreB - scoreA;
  });
}

/**
 * Extract salary number from salary string
 */
function extractSalaryNumber(salaryStr: string): number {
  const match = salaryStr.match(/\$?(\d+(?:,\d+)*(?:\.\d+)?)/);
  return match ? parseInt(match[1].replace(/,/g, '')) : 0;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Extract company name from Ashby job URL
 */
function extractCompanyFromUrl(url: string): string | null {
  try {
    const urlParts = url.split('/');
    const boardIndex = urlParts.findIndex(part => part === 'jobs');
    if (boardIndex > 0) {
      return urlParts[boardIndex - 1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    return null;
  } catch {
    return null;
  }
}

/**
 * Format location from Ashby job data
 */
function formatLocation(ashbyJob: AshbyJob): string {
  if (ashbyJob.isRemote) {
    return 'Remote';
  }

  if (ashbyJob.location) {
    return ashbyJob.location;
  }

  if (ashbyJob.address?.postalAddress) {
    const addr = ashbyJob.address.postalAddress;
    const parts = [addr.addressLocality, addr.addressRegion, addr.addressCountry].filter(Boolean);
    return parts.join(', ') || 'Location TBD';
  }

  return 'Location TBD';
}

/**
 * Format compensation from Ashby job data
 */
function formatCompensation(compensation?: AshbyCompensation): string {
  if (!compensation?.components?.length) {
    return 'Competitive';
  }

  const salaryComponents = compensation.components.filter(c => c.compensationType === 'Salary');

  if (salaryComponents.length > 0) {
    const salary = salaryComponents[0];
    if (salary.minValue && salary.maxValue) {
      const currency = salary.currencyCode || 'USD';
      return `${currency} ${salary.minValue.toLocaleString()} - ${salary.maxValue.toLocaleString()}`;
    }
    if (salary.summary) {
      return salary.summary;
    }
  }

  return 'Competitive';
}

/**
 * Generate tags from Ashby job data
 */
function generateTags(ashbyJob: AshbyJob): string[] {
  const tags: string[] = [];

  if (ashbyJob.isRemote) {
    tags.push('Remote');
  }

  if (ashbyJob.employmentType) {
    tags.push(mapEmploymentType(ashbyJob.employmentType));
  }

  if (ashbyJob.department) {
    tags.push(ashbyJob.department);
  }

  if (ashbyJob.team) {
    tags.push(ashbyJob.team);
  }

  return tags;
}

/**
 * Map Ashby employment type to readable format
 */
function mapEmploymentType(employmentType: string): string {
  const mapping: Record<string, string> = {
    'FullTime': 'Full-time',
    'PartTime': 'Part-time',
    'Intern': 'Internship',
    'Contract': 'Contract',
    'Temporary': 'Temporary'
  };

  return mapping[employmentType] || employmentType;
}

/**
 * Generate company logo URL
 */
function generateCompanyLogo(companyName: string): string {
  const cleanName = companyName.replace(/[^a-zA-Z0-9\s]/g, '').trim();
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(cleanName)}&background=random&size=150`;
}

/**
 * Normalize job data from different API formats
 */
function normalizeJobData(apiJob: any): any {
  // Handle different API response formats
  const job: any = {};

  // Title
  job.title = apiJob.title || apiJob.job_title || apiJob.position || '';

  // Company
  if (typeof apiJob.company === 'string') {
    job.company = apiJob.company;
  } else if (typeof apiJob.company_name === 'string') {
    job.company = apiJob.company_name;
  } else if (apiJob.company?.display_name) {
    job.company = apiJob.company.display_name;
  } else if (apiJob.company?.name) {
    job.company = apiJob.company.name;
  } else {
    job.company = 'Company';
  }

  // Location
  job.location = apiJob.location || apiJob.job_location || apiJob.candidate_required_location || 'Remote';

  // Description
  job.description = apiJob.description || apiJob.job_description || '';

  // Pay/Salary
  job.pay = apiJob.salary || apiJob.job_salary_range || apiJob.salary_min || 'Competitive';

  // Logo
  job.logo = apiJob.logo || apiJob.company_logo || '';

  // Apply URL
  job.applyUrl = apiJob.apply_url || apiJob.job_apply_link || apiJob.url || '';

  // Posted date
  job.posted_at = apiJob.posted_at || apiJob.job_posted_date || apiJob.date || '';

  // ID
  job.id = apiJob.id || apiJob.job_id || '';

  // Tags
  job.tags = apiJob.tags || [];

  return job;
}
