// authUtils.ts
// Consolidated authentication utilities for Jobbify - includes auth checks, Google auth, and utility functions

import { useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import * as AuthSession from 'expo-auth-session';
import Constants from 'expo-constants';
import { Platform, Alert } from 'react-native';

// ============================================================================
// CONFIGURATION
// ============================================================================

// Google OAuth Client IDs
const GOOGLE_WEB_CLIENT_ID = '363643098728-153alopt4kga1c3fffp2cuuos3ethcch.apps.googleusercontent.com';
const GOOGLE_IOS_CLIENT_ID = '363643098728-h7ardkqauedqsddnpri3ef026etkf4rs.apps.googleusercontent.com';

// ============================================================================
// AUTH CHECK HOOK
// ============================================================================

/**
 * Hook to enforce authentication and redirect as needed
 */
export function useAuthCheck(options?: { redirectTo?: string; redirectIfAuthed?: boolean }) {
  const { setUser } = useAppContext();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get the current session
        const { data: { session } } = await supabase.auth.getSession();

        // Update user in context if we have a session
        if (session?.user) {
          // Format the user object to match your app's User type
          setUser({
            id: session.user.id,
            name: session.user.user_metadata?.name || 'User',
            email: session.user.email || '',
            avatar: session.user.user_metadata?.avatar_url || '',
            skills: [],
            experience: [],
          });

          // If we should redirect authenticated users away from auth pages
          if (options?.redirectIfAuthed) {
            // Add a small delay to ensure root layout is mounted
            setTimeout(() => {
              try {
                router.replace('/(tabs)');
              } catch (navError) {
                console.error('Navigation error:', navError);
              }
            }, 100);
          }
        } else {
          // No session, set user to null
          setUser(null);

          // If we should redirect unauthenticated users to login
          if (!options?.redirectIfAuthed) {
            // Add a small delay to ensure root layout is mounted
            setTimeout(() => {
              try {
                if (options?.redirectTo) {
                  router.replace(options.redirectTo);
                } else {
                  router.replace('/(auth)/login');
                }
              } catch (navError) {
                console.error('Navigation error:', navError);
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        setUser(null);
      }
    };

    checkAuth();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        if (!session && !options?.redirectIfAuthed) {
          // User has been logged out, redirect to login
          if (options?.redirectTo) {
            router.replace(options.redirectTo);
          } else {
            router.replace('/(auth)/login');
          }
        } else if (session && options?.redirectIfAuthed) {
          // User has logged in, redirect away from auth pages
          router.replace('/(tabs)');
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);
}

// ============================================================================
// OAUTH CONFIGURATION UTILITIES
// ============================================================================

/**
 * Gets the appropriate redirect URI for OAuth authentication
 * For Supabase authentication, the key is to understand that:
 * 1. Supabase handles the OAuth redirect internally
 * 2. Your app needs to handle the callback after auth is complete
 */
export function getAuthRedirectUri(): string {
  try {
    // For Supabase mobile authentication, we need a URL that:
    // 1. Works across networks and devices
    // 2. Can be opened by WebBrowser.openAuthSessionAsync
    // 3. Will eventually redirect back to our app

    if (isRunningInExpoGo()) {
      // For Expo Go, we need to use Expo's authentication proxy
      // The key is this URL should be able to redirect back to Expo Go
      const redirectUrl = 'https://auth.expo.io/@bigpooper/jobbify';
      console.log('Using Expo authentication proxy for callback:', redirectUrl);
      return redirectUrl;
    } else {
      // For production builds, use the Supabase URL with public TLD that Google accepts
      // This is already configured in your Supabase and Google Cloud Console
      const redirectUrl = 'https://ubueawlkwlvgzxcslats.supabase.co/auth/v1/callback';
      console.log('Using Supabase callback URL for production:', redirectUrl);
      return redirectUrl;
    }
  } catch (error) {
    console.error('Error generating redirect URI:', error);
    // Fallback to the Supabase URL as this will always work for production
    return 'https://ubueawlkwlvgzxcslats.supabase.co/auth/v1/callback';
  }
}

/**
 * Determines if the app is running in Expo Go
 */
export function isRunningInExpoGo(): boolean {
  // Check if we're running in Expo Go
  // This is a heuristic and might need adjustment
  const appOwnership = Constants.appOwnership;
  return appOwnership === 'expo';
}

/**
 * Gets the Google OAuth configuration based on the platform and environment
 */
export function getGoogleOAuthConfig() {
  // Use our defined constants rather than trying to read from config
  const redirectUri = getAuthRedirectUri();

  // Check if we're using the Expo proxy or a direct deep link
  const isUsingExpoProxy = redirectUri.includes('auth.expo.io');

  // Determine the appropriate client ID based on platform
  let clientId = GOOGLE_WEB_CLIENT_ID; // Default to web client ID

  if (Platform.OS === 'ios') {
    // For iOS, we need to consider native auth or proxy usage
    if (isUsingExpoProxy) {
      console.log('Using Web Client ID for iOS with Expo proxy');
      clientId = GOOGLE_WEB_CLIENT_ID;
    } else {
      console.log('Using iOS Client ID for native iOS authentication');
      clientId = GOOGLE_IOS_CLIENT_ID;
    }
  } else {
    // For Android and web
    console.log('Using Web Client ID for non-iOS platforms');
    clientId = GOOGLE_WEB_CLIENT_ID;
  }

  // Build and return the complete config object
  return {
    provider: 'google' as const,
    clientId,
    redirectUri,
    options: {
      redirectTo: redirectUri,
      skipBrowserRedirect: Platform.OS === 'ios' ? false : true
    }
  };
}

// ============================================================================
// GOOGLE AUTHENTICATION
// ============================================================================

/**
 * Development mode login helper
 */
async function developmentModeLogin() {
  return new Promise((resolve) => {
    Alert.alert(
      'Development Mode',
      'Choose authentication method:',
      [
        {
          text: 'Use Google OAuth',
          onPress: async () => {
            try {
              const config = getGoogleOAuthConfig();
              const { error } = await supabase.auth.signInWithOAuth(config);
              if (error) throw error;
              resolve({ success: true });
            } catch (err) {
              console.error('[googleAuth] OAuth error:', err);
              resolve({ success: false, error: err });
            }
          }
        },
        {
          text: 'Quick Test Login',
          onPress: async () => {
            try {
              // For development, you can use a test account
              const { error } = await supabase.auth.signInWithPassword({
                email: '<EMAIL>',
                password: 'testpassword123'
              });
              if (error) throw error;
              resolve({ success: true });
            } catch (err) {
              console.error('[googleAuth] Test login error:', err);
              resolve({ success: false, error: err });
            }
          }
        },
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => resolve({ success: false, error: 'Cancelled' })
        }
      ]
    );
  });
}

/**
 * Handles Google authentication - optimized for reliable operation
 * in both development and production environments
 */
export async function signInWithGoogle() {
  // For development, give the option to use direct login
  if (__DEV__) {
    return developmentModeLogin();
  }

  // For production builds, use the standard OAuth flow
  try {
    console.log('[googleAuth] Starting Google sign-in process');
    // Get platform-aware config
    const config = getGoogleOAuthConfig();

    const { error } = await supabase.auth.signInWithOAuth(config);

    if (error) throw error;
    return { success: true };
  } catch (err) {
    console.error('[googleAuth] Authentication error:', err);
    Alert.alert('Authentication Error', String(err));
    return { success: false, error: err };
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Sign out the current user
 */
export async function signOut() {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error signing out:', error);
    return { success: false, error };
  }
}

/**
 * Get the current user session
 */
export async function getCurrentSession() {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    return { session, error: null };
  } catch (error) {
    console.error('Error getting session:', error);
    return { session: null, error };
  }
}

/**
 * Get the current user
 */
export async function getCurrentUser() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return { user, error: null };
  } catch (error) {
    console.error('Error getting user:', error);
    return { user: null, error };
  }
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const { session } = await getCurrentSession();
    return !!session;
  } catch {
    return false;
  }
}

/**
 * Refresh the current session
 */
export async function refreshSession() {
  try {
    const { data, error } = await supabase.auth.refreshSession();
    if (error) throw error;
    return { session: data.session, error: null };
  } catch (error) {
    console.error('Error refreshing session:', error);
    return { session: null, error };
  }
}
